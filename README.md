# Introduction
GallagherAI - Application to chat with an OpenAI model including your own file(s).

## Build Status

| App | DEV | TEST | MAIN |
| --- | --- | --- | --- |
| Front End | [![Build Status](https://dev.azure.com/ajg-corp/GallagherGPT/_apis/build/status%2FGallagherAI-Code%2FFrontend-CD?branchName=dev)](https://dev.azure.com/ajg-corp/GallagherGPT/_build/latest?definitionId=1401&branchName=dev) | [![Build Status](https://dev.azure.com/ajg-corp/GallagherGPT/_apis/build/status%2FGallagherAI-Code%2FFrontend-CD?branchName=test)](https://dev.azure.com/ajg-corp/GallagherGPT/_build/latest?definitionId=1401&branchName=test) | [![Build Status](https://dev.azure.com/ajg-corp/GallagherGPT/_apis/build/status%2FGallagherAI-Code%2FFrontend-CD?branchName=main)](https://dev.azure.com/ajg-corp/GallagherGPT/_build/latest?definitionId=1401&branchName=main) |
| Back End | [![Build Status](https://dev.azure.com/ajg-corp/GallagherGPT/_apis/build/status%2FGallagherAI-Code%2FBackend-CD?branchName=dev)](https://dev.azure.com/ajg-corp/GallagherGPT/_build/latest?definitionId=1400&branchName=dev) | [![Build Status](https://dev.azure.com/ajg-corp/GallagherGPT/_apis/build/status%2FGallagherAI-Code%2FBackend-CD?branchName=test)](https://dev.azure.com/ajg-corp/GallagherGPT/_build/latest?definitionId=1400&branchName=test) | [![Build Status](https://dev.azure.com/ajg-corp/GallagherGPT/_apis/build/status%2FGallagherAI-Code%2FBackend-CD?branchName=main)](https://dev.azure.com/ajg-corp/GallagherGPT/_build/latest?definitionId=1400&branchName=main) |

## Architecture

### State Management Enhancement (v2.0)

The application has been significantly refactored to implement Redux Toolkit (RTK) with RTK Query for improved state management and API handling. This enhancement provides:

- **Centralized State Management**: All application state is managed through a single Redux store
- **Automatic API Caching**: RTK Query provides intelligent caching and background refetching
- **Type Safety**: Full TypeScript integration with typed hooks and API responses
- **Performance Optimization**: Reduced re-renders and optimized data fetching
- **Error Handling**: Centralized error states and automatic retry logic

For detailed technical documentation, see [State Management Documentation](docs/state-management-enhancement.md).

## gitflow

For source code management, we are using a process very close to [gitflow](https://www.atlassian.com/git/tutorials/comparing-workflows/gitflow-workflow).

### Key branches
`main` - Official release history main branch.

`dev` - Integration branch for features.

`test` - Test branch that allows deploying to test environment. IaC team wants to use this branch also for infra needs. They look for this branch in their pipelines.

`feature/[branch-name]` - Branch off from `dev` for feature development.

`release/[branch-name]` - Branched from `dev` and used for last-minute updates before a release. Eg. updating the changelog. This will be merged back into `dev` branch

### Workflow

1. **Branch creation**: Create a new branch from `dev` when new feature work begins.
2. **Deploy to dev environment**: It's ok while developing to deploy to the dev environment from one's feature branch to test something. We can always deploy again from the `dev` branch to sync up the dev environment.
3. **Pull Requests**: Once a feature or bug is complete, submit a PR to `dev`. Suggest deleting the feature branch at this point.
4. **Review and CI**: At least one other developer needs to review the merge to `dev` branch.
5. **Test branch**: PR from `dev` branch to `test` branch when wanting to deploy to test environment.
6. **Release branch**: Create from `dev` branch to update any last minute issues like changelogs and readmes. PR this to `main` branch once ready. Also PR this to `dev` branch to keep it in sync. The release branch is not needed to be kept once merged into `main` branch.

![Release branch flow](docs/images/releaseflow-v2.png)

7. **Tags**: Once a deployment to production is complete from the `main` branch, tag the `main` branch following this pattern: yyyymmdd.

### How to prep for deployment to Production....easily

1. Create release branch from dev branch. Name it in this format: `release\[yyyymmdd]`
2. Update the CHANGELOG of the root with all changes going live.
3. Update the frontend 'updates' page located here: `/src/components/TabsSection/UpdatesTab.tsx`
4. Update the frontend version file so the 'red dot' shows up for the 'Updates' page: `src/frontend/public/version.json`
5. Check on dependencies on backend/frontend/infra to make sure we are using the latest versions.
6. Once everything is tested in DEV environment, deploy to TEST environment.
7. Work with the Change Manager on the Remedy CRQ that will be required to complete the deployment.
8. PR the Release branch to Main branch.
9. Main branch is used to deploy to production.
10. POST-DEPLOY, Tag the Main branch to a new tag.
11. POST-DEPLOY, PR the Release branch merge to Dev branch.
12. Delete the Release branch.