# Redux and RTK Query Implementation

This directory contains the core Redux configuration and RTK Query implementation for the application.

## Overview

The application uses Redux Toolkit for state management and RTK Query for API calls. This approach provides several benefits:

- **Centralized State Management**: All application state is managed in a single store
- **Automatic Caching**: RTK Query automatically caches API responses
- **Automatic Loading and Error States**: RTK Query provides loading and error states for API calls
- **Automatic Refetching**: RTK Query can automatically refetch data when needed
- **TypeScript Integration**: Full TypeScript support for type safety

## Directory Structure

- `app/api.ts`: Base RTK Query API configuration
- `app/hooks.ts`: Typed hooks for using Redux
- `services/api/`: API service implementations
  - `workspacesApi.ts`: Workspaces API
  - `documentsApi.ts`: Documents API
  - `chatApi.ts`: Chat API
  - `translationApi.ts`: Translation API
  - `settingsApi.ts`: Settings API (local IndexedDB)

## Usage

### Using Redux State

To access Redux state in a component, use the `useAppSelector` hook:

```tsx
import { useAppSelector } from '../app/hooks';

const MyComponent = () => {
  const user = useAppSelector((state) => state.user);
  
  return <div>Hello, {user.name}</div>;
};
```

### Dispatching Actions

To dispatch actions, use the `useAppDispatch` hook:

```tsx
import { useAppDispatch } from '../app/hooks';
import { setName } from '../features/userSlice';

const MyComponent = () => {
  const dispatch = useAppDispatch();
  
  const handleNameChange = (name: string) => {
    dispatch(setName(name));
  };
  
  return <button onClick={() => handleNameChange('John')}>Set Name</button>;
};
```

### Making API Calls

To make API calls, use the RTK Query hooks:

```tsx
import { useGetWorkspacesQuery } from '../services/api/workspacesApi';

const MyComponent = () => {
  const { data: workspaces, isLoading, error } = useGetWorkspacesQuery();
  
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.toString()}</div>;
  
  return (
    <div>
      <h1>Workspaces</h1>
      <ul>
        {workspaces?.map((workspace) => (
          <li key={workspace.id}>{workspace.name}</li>
        ))}
      </ul>
    </div>
  );
};
```

### Mutations (Create, Update, Delete)

To perform mutations, use the RTK Query mutation hooks:

```tsx
import { useCreateWorkspaceMutation } from '../services/api/workspacesApi';

const MyComponent = () => {
  const [createWorkspace, { isLoading }] = useCreateWorkspaceMutation();
  
  const handleCreateWorkspace = async () => {
    try {
      const result = await createWorkspace({ name: 'New Workspace', description: 'Description' }).unwrap();
      console.log('Created workspace:', result);
    } catch (error) {
      console.error('Failed to create workspace:', error);
    }
  };
  
  return (
    <button onClick={handleCreateWorkspace} disabled={isLoading}>
      {isLoading ? 'Creating...' : 'Create Workspace'}
    </button>
  );
};
```

## Authentication

API calls automatically include the authentication token from the Redux store. The token is refreshed using the `useApiWithAuth` hook, which should be used in components that make API calls.

```tsx
import useApiWithAuth from '../hooks/useApiWithAuth';

const MyComponent = () => {
  const { isAuthenticated } = useApiWithAuth();
  
  // Rest of component...
};
```
