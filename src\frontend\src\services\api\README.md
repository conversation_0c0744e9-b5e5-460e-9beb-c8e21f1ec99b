# API Services with Redux Toolkit Query

This directory contains the API services implemented with Redux Toolkit Query (RTK Query) for the application.

## Structure

- `api.ts`: Base RTK Query configuration  
- `chatApi.ts`: API for the chat  
- `documentsApi.ts`: API for documents  
- `settingsApi.ts`: API for settings (uses IndexedDB)  
- `translationApi.ts`: API for translation  
- `workspacesApi.ts`: API for workspaces  

## Features

- **Automatic cache management**: RTK Query automatically handles response caching  
- **Loading and error states**: Provides loading and error states for API calls  
- **Cache invalidation**: Allows cache invalidation when performing mutations  
- **Full typing**: Complete TypeScript support for type safety  

## Usage

### Queries

To perform API queries, use the hooks generated by RTK Query:

```tsx
import { useGetWorkspacesQuery } from '../services/api/workspacesApi';

const MyComponent = () => {
  const { data: workspaces, isLoading, error } = useGetWorkspacesQuery();
  
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.toString()}</div>;
  
  return (
    <div>
      <h1>Workspaces</h1>
      <ul>
        {workspaces?.map((workspace) => (
          <li key={workspace.id}>{workspace.name}</li>
        ))}
      </ul>
    </div>
  );
};
```

### Mutations (Create, Update, Delete)

To perform mutations, use the mutation hooks generated by RTK Query:

```tsx
import { useCreateWorkspaceMutation } from '../services/api/workspacesApi';

const MyComponent = () => {
  const [createWorkspace, { isLoading }] = useCreateWorkspaceMutation();
  
  const handleCreateWorkspace = async () => {
    try {
      const result = await createWorkspace({ 
        name: 'New workspace', 
        description: 'Description' 
      }).unwrap();
      
      console.log('Workspace created:', result);
    } catch (error) {
      console.error('Error creating workspace:', error);
    }
  };
  
  return (
    <button onClick={handleCreateWorkspace} disabled={isLoading}>
      {isLoading ? 'Creating...' : 'Create workspace'}
    </button>
  );
};
```

### Response Streaming

For response streaming (such as in chat), helper functions are used:

```tsx
import { streamChatResponse, ChatRequest } from '../services/api/chatApi';

const handleSendMessage = async (message: string) => {
  const chatRequest: ChatRequest = {
    workspace_id: workspaceId,
    settings: {},
    messages: [
      { 
        role: 'user', 
        content: [{ type: 'text', value: message }] 
      }
    ]
  };
  
  try {
    await streamChatResponse(
      chatRequest,
      token,
      (chunk) => {
        // Process each response chunk
        console.log('Chunk received:', chunk);
      }
    );
  } catch (error) {
    console.error('Streaming error:', error);
  }
};
```

## Authentication

API calls automatically include the authentication token from the Redux store. The token is updated using the `useApiWithAuth` hook, which must be used in components that make API calls.

```tsx
import useApiWithAuth from '../hooks/useApiWithAuth';

const MyComponent = () => {
  const { isAuthenticated } = useApiWithAuth();
  
  // Rest of the component...
};
```
