# React GallagherAI Frontend Client

This is the frontend UI for GallagherAI, which connects to Azure OpenAI services through a backend that handles requests and responses.

## Tech Stack
- **Vite**: a modern build tool that provides a fast development environment for web projects. It leverages native ES modules and offers instant server start, lightning-fast hot module replacement (HMR), and optimized builds.
- **React** (Typescript + SWC template): for building the user interface of this (single-page) application. Using TypeScript to add static typing, which helps catch errors early and improves code quality. And SWC (Speedy Web Compiler), a super-fast compiler that can be used to transform and bundle JavaScript and TypeScript code.
- **Redux**: to manage the state of the application in a more modular way, making it easier to share state between components.
- **TailwindCSS**: CSS framwork for rapid UI development with a consistent design system.
- **MSAL (Microsoft Authentication Library)**: An authentication library provided by Microsoft to handle authorization with Azure Active Directory and other Microsoft identity platforms.
- **Fluentui/react-icons**: a library that provides a collection of SVG-based icons wrapped in React components

## Prerequisites
- **Node.js 18+**

## Running the Project

1. **Navigate to the Project Directory**:
    ```bash
    cd GallagherAI/src/frontend
    ```

2. **Enable `pnpm` to Manage Project Dependencies**:
    ```bash
    npm install -g pnpm
    ```

3. **Install Dependencies**:
    ```bash
    pnpm install
    ```

4. **Start the App for Local Development/Testing**:
    ```bash
    pnpm run localhost
    ```

## Building for Production

There's a similar command to generate static files on a /dist folder in the root of the frontend project, this command allow you to minify the scripts, html files and export assets files, so you can deploy those files in a production environment, the command for that is:
```bash
pnpm run build
````

## Testing Backend Responses

To test responses from the backend using Git Bash (or any Bash-based terminal), use the following command:
```bash  
curl -X POST http://localhost:5280/Chat -H "Content-Type: application/json" -d '{"messages": [{"Role": "user", "Content": "Hello"}]}'
```

## Get the Unit-test coverage in browser

1. **Navigate to the Project Directory**:
    ```bash
    cd GallagherAI/src/frontend
    ```
2. **Run the command**:
    ```bash
    npx vite preview --outDir html
    ```
3. **Navigate to the browser on given port**:
    ```bash
    http://localhost:4173/
    ```

## Get the Unit-test coverage in CLI

1. **Navigate to the Project Directory**:
    ```bash
    cd GallagherAI/src/frontend
    ```
2. **Run the command**:
    ```bash
    pnpm run coverage
    ```
