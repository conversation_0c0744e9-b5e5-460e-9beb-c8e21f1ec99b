# Frontend Documentation Structure

## Overview

This document outlines the organization of frontend documentation within the GallagherAI project.

## Documentation Hierarchy

```
src/frontend/
├── README.md                           # Setup guide for new developers
├── docs/                              # Technical documentation
│   ├── README.md                      # Documentation index and overview
│   ├── state-management-guide.md      # Comprehensive Redux/RTK guide
│   ├── redux-migration-guide.md       # Migration patterns and examples
│   └── DOCUMENTATION_STRUCTURE.md     # This file
├── src/
│   ├── app/
│   │   └── README.md                  # Redux usage patterns
│   └── services/api/
│       └── README.md                  # API services reference
```

## Documentation Types

### 1. Setup Documentation
**Location**: `src/frontend/README.md`
**Purpose**: Getting started guide for new developers
**Content**:
- Tech stack overview
- Prerequisites and installation
- Running the project
- Building for production
- Testing instructions

### 2. Technical Documentation
**Location**: `src/frontend/docs/`
**Purpose**: Comprehensive guides for developers working with the codebase
**Content**:
- State management patterns
- API integration guides
- Migration instructions
- Best practices and troubleshooting

### 3. Code-Level Documentation
**Location**: Within source code directories
**Purpose**: Specific implementation details and usage examples
**Content**:
- Redux patterns (`src/app/README.md`)
- API services reference (`src/services/api/README.md`)

## Quick Navigation

### For New Developers
1. Start with [Frontend Setup Guide](../README.md)
2. Review [Technical Documentation Overview](README.md)
3. Explore [State Management Guide](state-management-guide.md)

### For Existing Developers
1. Check [Migration Guide](redux-migration-guide.md) for pattern updates
2. Reference [API Documentation](../src/services/api/README.md) for endpoints
3. Use [Redux Guide](../src/app/README.md) for state management patterns

### For Code Reviewers
1. Review [Best Practices](state-management-guide.md#best-practices)
2. Check [Troubleshooting](state-management-guide.md#troubleshooting)
3. Verify [Pattern Consistency](redux-migration-guide.md#common-migration-patterns)

## Maintenance

### When to Update Documentation

1. **New Features**: Update relevant guides when adding new functionality
2. **Pattern Changes**: Update migration guide when changing established patterns
3. **API Changes**: Update API documentation when modifying endpoints
4. **Setup Changes**: Update setup guide when changing build/dev processes

### Documentation Standards

1. **Code Examples**: Always include practical, working examples
2. **Path References**: Use relative paths from the document location
3. **Cross-References**: Link between related documentation sections
4. **Versioning**: Keep documentation current with the codebase

## External References

- [Main Project Documentation](../../../docs/) - Project-wide documentation
- [Architecture Decisions](../../../docs/architecturedecisions/) - ADRs and design decisions
- [Infrastructure Documentation](../../../docs/IaC_HowTo_Documentation_Links.md) - Deployment and infrastructure

## Support

For questions about documentation:
1. Check existing documentation first
2. Search project issues for similar questions
3. Contact the development team
4. Create an issue with detailed information if needed
